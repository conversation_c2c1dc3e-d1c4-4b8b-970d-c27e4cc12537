"use client";

import { useState, useEffect } from "react";
import { useDiseaseData } from "../contexts/DiseaseDataContext";
import { generateDiseaseAnalysisWithOpenRouter } from "../services/openRouterService";

interface AIAnalysisModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface AIAnalysisResult {
  analysis: string;
  loading: boolean;
  error: string | null;
}

export default function AIAnalysisModal({ isOpen, onClose }: AIAnalysisModalProps) {
  const { processedData } = useDiseaseData();
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult>({
    analysis: "",
    loading: false,
    error: null
  });

  // preload na ang AI analysis pag open ang modal ug naa nay data
  // para dili na mag wait pa ang user, instant na dayon
  useEffect(() => {
    if (isOpen && Object.keys(processedData).length > 0 && !analysisResult.analysis && !analysisResult.loading) {
      generateAIAnalysis(); // go na dayon!
    }
  }, [isOpen, processedData]);

  const generateAIAnalysis = async () => {
    setAnalysisResult({ analysis: "", loading: true, error: null }); // reset everything muna

    try {
      // gihimo nako comprehensive ang data para sa AI - dapat complete jud
      const dataForAI = {
        totalDiseases: Object.keys(processedData).length, // pila ka diseases tanan
        diseaseBreakdown: Object.entries(processedData).map(([disease, data]) => ({
          disease: disease,
          totalCases: data.totalCases,
          municipalities: data.municipalities, // kada municipality ug ilang cases
          municipalityCount: Object.keys(data.municipalities).length,
          averageCasesPerMunicipality: data.totalCases / Math.max(Object.keys(data.municipalities).length, 1) // average para fair
        })),
        totalCases: Object.values(processedData).reduce((sum, data) => sum + data.totalCases, 0), // total tanan cases
        municipalities: ["Mandaue", "Consolacion", "Lilo-an"], // ang atong 3 municipalities ra jud
        lastUpdated: new Date().toISOString(), // timestamp para updated
        topDiseases: Object.entries(processedData)
          .sort(([,a], [,b]) => b.totalCases - a.totalCases) // sort by highest cases
          .slice(0, 5) // top 5 ra
          .map(([disease, data]) => ({ disease, cases: data.totalCases }))
      };

      // gamit ta ang OpenRouter API with Moonshot AI Kimi - libre man ni siya
      const result = await generateDiseaseAnalysisWithOpenRouter(processedData);

      if (result.success) {
        // naa nay response from AI, parse nato ug combine sa structured data
        const analysisData = parseAIResponseToStructuredData(result.analysis, dataForAI);

        setAnalysisResult({
          analysis: JSON.stringify(analysisData), // JSON string para ma parse later
          loading: false,
          error: null
        });
      } else {
        // kung nag fail ang API, fallback ta sa structured analysis - backup plan jud
        console.warn('OpenRouter API failed, using fallback analysis:', result.error);
        const analysisData = generateDynamicAnalysis(dataForAI);

        // add pa ang fallback conclusion para naa gihapon
        analysisData.aiConclusion = generateAIConclusion(dataForAI, "");

        setAnalysisResult({
          analysis: JSON.stringify(analysisData),
          loading: false,
          error: null
        });
      }

    } catch (error: any) {
      console.error('AI Analysis Error:', error); // nag error, log nato para makita
      setAnalysisResult({
        analysis: "",
        loading: false,
        error: "Failed to generate AI analysis. Please try again." // simple error message ra
      });
    }
  };

  // parse ang AI response ug combine sa structured data - para professional ang display
  const parseAIResponseToStructuredData = (aiResponse: string, dataForAI: any) => {
    // kung naa AI response, combine nato sa structured data
    const structuredData = generateDynamicAnalysis(dataForAI);

    // generate ang AI conclusion nga naka incorporate ang AI insights
    structuredData.aiConclusion = generateAIConclusion(dataForAI, aiResponse);

    return structuredData; // return na ang combined data
  };

  // generate ang AI conclusion summary - ito ang main conclusion sa bottom
  const generateAIConclusion = (dataForAI: any, aiResponse: string) => {
    const totalCases = dataForAI.totalCases;
    const diseaseCount = dataForAI.totalDiseases;
    const topDisease = dataForAI.topDiseases[0];
    const currentDate = new Date();
    const currentMonth = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

    // determine ang risk level based sa total cases - simple logic ra
    const riskLevel = totalCases > 100 ? 'HIGH' : totalCases > 50 ? 'MEDIUM' : 'LOW';
    const urgencyLevel = totalCases > 100 ? 'immediate' : totalCases > 50 ? 'prompt' : 'routine';

    // kung naa AI response, gamiton nato as conclusion, kung wala fallback ta
    if (aiResponse && aiResponse.trim().length > 50) {
      return `
Based on the comprehensive analysis of disease surveillance data across Mandaue, Consolacion, and Lilo-an municipalities as of ${currentMonth}, the AI analysis provides the following conclusion:

${aiResponse}

*This AI-powered analysis provides evidence-based insights for public health decision-making in the three-municipality surveillance area.*
      `.trim();
    }

    // Fallback structured conclusion
    return `
Based on the comprehensive analysis of disease surveillance data across Mandaue, Consolacion, and Lilo-an municipalities as of ${currentMonth}, the following key conclusions have been reached:

**Overall Assessment**: The current disease burden shows a ${riskLevel} risk level with ${totalCases} total cases across ${diseaseCount} monitored diseases. ${topDisease?.disease} emerges as the primary concern with ${topDisease?.cases} reported cases, requiring ${urgencyLevel} attention from health authorities.

**Critical Findings**:
• Multi-municipal spread patterns indicate the need for coordinated response strategies
• Seasonal factors suggest potential fluctuations in disease transmission over the next 6 months
• Resource allocation should prioritize high-burden areas while maintaining surveillance in all municipalities

**Strategic Recommendations**:
Health officials should implement ${urgencyLevel} intervention measures, focusing on vector control, community education, and enhanced surveillance protocols. The predictive analysis indicates varying trends across different diseases, necessitating tailored approaches for each municipality.

**Next Steps**:
Continue weekly monitoring, maintain alert thresholds, and prepare for seasonal variations. Regular reassessment of this analysis is recommended as new surveillance data becomes available.

*This analysis provides a foundation for evidence-based public health decision-making in the three-municipality surveillance area.*
    `.trim();
  };

  // Generate dynamic analysis based on actual data - para realistic ang output
  const generateDynamicAnalysis = (data: any) => {
    const totalCases = data.totalCases;
    const diseaseCount = data.totalDiseases;
    const topDisease = data.topDiseases[0];
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Generate predictive data for next 6 months
    const generateMonthlyPredictions = () => {
      const predictions = [];
      for (let i = 1; i <= 6; i++) {
        const futureDate = new Date(currentYear, currentMonth + i, 1);
        const monthName = futureDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

        // Seasonal factors based on Philippines disease patterns
        const seasonalFactors = {
          dengue: currentMonth >= 5 && currentMonth <= 10 ? 1.4 : 0.7, // Rainy season
          malaria: currentMonth >= 5 && currentMonth <= 10 ? 1.3 : 0.8,
          cholera: currentMonth >= 3 && currentMonth <= 8 ? 1.2 : 0.6,
          tuberculosis: currentMonth >= 11 || currentMonth <= 2 ? 1.1 : 0.9, // Cooler months
          measles: currentMonth >= 0 && currentMonth <= 4 ? 1.3 : 0.8, // Dry season
          covid: 1.0 + (Math.sin((currentMonth + i) * Math.PI / 6) * 0.2) // Cyclical pattern
        };

        // Calculate predictions for each disease
        const diseasePredictions = data.diseaseBreakdown.map((disease: any) => {
          const baseCases = disease.totalCases;
          const seasonalFactor = seasonalFactors[disease.disease as keyof typeof seasonalFactors] || 1.0;
          const trendFactor = 1 + (Math.random() - 0.5) * 0.3; // Random variation
          const predictedCases = Math.max(0, Math.round(baseCases * seasonalFactor * trendFactor));
          const changePercent = ((predictedCases - baseCases) / baseCases * 100).toFixed(1);

          return {
            disease: disease.disease,
            current: baseCases,
            predicted: predictedCases,
            change: changePercent,
            trend: predictedCases > baseCases ? 'increase' : predictedCases < baseCases ? 'decrease' : 'stable'
          };
        });

        predictions.push({
          month: monthName,
          diseases: diseasePredictions,
          totalPredicted: diseasePredictions.reduce((sum, d) => sum + d.predicted, 0)
        });
      }
      return predictions;
    };

    const monthlyPredictions = generateMonthlyPredictions();

    return {
      executiveSummary: {
        totalCases,
        diseaseCount,
        topDisease: topDisease?.disease || 'N/A',
        topDiseaseCount: topDisease?.cases || 0,
        riskLevel: totalCases > 100 ? 'HIGH' : totalCases > 50 ? 'MEDIUM' : 'LOW',
        municipalities: data.municipalities.length
      },
      diseaseBreakdown: data.diseaseBreakdown.map((disease: any) => ({
        ...disease,
        riskLevel: disease.totalCases > 50 ? 'HIGH' : disease.totalCases > 20 ? 'MEDIUM' : 'LOW',
        riskColor: disease.totalCases > 50 ? '#DC2626' : disease.totalCases > 20 ? '#F59E0B' : '#10B981'
      })),
      municipalComparison: data.diseaseBreakdown.map((disease: any) => {
        const municipalities = Object.entries(disease.municipalities);
        const highest = municipalities.sort(([,a], [,b]) => (b as number) - (a as number))[0];
        return {
          disease: disease.disease,
          highestMunicipality: highest?.[0] || 'N/A',
          highestCount: highest?.[1] || 0,
          distribution: municipalities
        };
      }),
      predictions: monthlyPredictions,
      recommendations: generateRecommendations(data, totalCases)
    };
  };

  const generateRecommendations = (data: any, totalCases: number) => {
    const currentMonth = new Date().getMonth();
    const isRainySeason = currentMonth >= 5 && currentMonth <= 10;

    return {
      immediate: [
        totalCases > 100 ? 'Activate emergency response protocols' : 'Maintain regular surveillance',
        isRainySeason ? 'Intensify vector control measures' : 'Prepare for seasonal disease patterns',
        'Coordinate inter-municipal health responses'
      ],
      prevention: [
        'Community health education campaigns',
        isRainySeason ? 'Eliminate standing water sources' : 'Prepare for upcoming rainy season',
        'Strengthen healthcare worker capacity'
      ],
      monitoring: data.diseaseBreakdown.map((disease: any) => ({
        disease: disease.disease,
        threshold: Math.ceil(disease.totalCases * 1.5),
        action: disease.totalCases > 30 ? 'Weekly monitoring required' : 'Monthly monitoring sufficient'
      }))
    };
  };

  const handleRetry = () => {
    generateAIAnalysis();
  };

  // Professional Analysis Display Component
  const AnalysisDisplay = ({ analysisData }: { analysisData: any }) => {
    return (
      <div className="space-y-6">
        {/* Executive Summary */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-blue-900">Executive Summary</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="text-2xl font-bold text-blue-600">{analysisData.executiveSummary.totalCases}</div>
              <div className="text-sm text-gray-600">Total Cases</div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="text-2xl font-bold text-blue-600">{analysisData.executiveSummary.diseaseCount}</div>
              <div className="text-sm text-gray-600">Diseases Monitored</div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className={`text-2xl font-bold ${
                analysisData.executiveSummary.riskLevel === 'HIGH' ? 'text-red-600' :
                analysisData.executiveSummary.riskLevel === 'MEDIUM' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {analysisData.executiveSummary.riskLevel}
              </div>
              <div className="text-sm text-gray-600">Risk Level</div>
            </div>
          </div>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800">
              <strong>Top Disease:</strong> {analysisData.executiveSummary.topDisease}
              ({analysisData.executiveSummary.topDiseaseCount} cases) across {analysisData.executiveSummary.municipalities} municipalities
            </p>
            <p className="text-blue-600 text-sm mt-2">
              📊 <strong>Global View:</strong> This analysis combines data from all municipalities (Mandaue, Consolacion, Lilo-an) - visible to all users regardless of their assigned municipality.
            </p>
          </div>
        </div>

        {/* Disease-Specific Risk Assessment */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900">Disease-Specific Risk Assessment</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {analysisData.diseaseBreakdown.map((disease: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900 capitalize">{disease.disease}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    disease.riskLevel === 'HIGH' ? 'bg-red-100 text-red-800' :
                    disease.riskLevel === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {disease.riskLevel}
                  </span>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Cases: <span className="font-medium text-gray-900">{disease.totalCases}</span></div>
                  <div>Municipalities: <span className="font-medium text-gray-900">{disease.municipalityCount}</span></div>
                  <div>Avg per Municipality: <span className="font-medium text-gray-900">{disease.averageCasesPerMunicipality.toFixed(1)}</span></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Predictive Forecasting */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-purple-900">6-Month Predictive Forecast</h3>
          </div>
          <div className="space-y-4">
            {analysisData.predictions.slice(0, 3).map((prediction: any, index: number) => (
              <div key={index} className="bg-white rounded-lg p-4 border border-purple-100">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-purple-900">{prediction.month}</h4>
                  <div className="text-sm text-purple-600">
                    Total Predicted: <span className="font-bold">{prediction.totalPredicted}</span> cases
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {prediction.diseases.slice(0, 3).map((disease: any, diseaseIndex: number) => (
                    <div key={diseaseIndex} className="bg-purple-50 rounded-lg p-3">
                      <div className="font-medium text-purple-900 capitalize text-sm">{disease.disease}</div>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-lg font-bold text-purple-700">{disease.predicted}</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          disease.trend === 'increase' ? 'bg-red-100 text-red-700' :
                          disease.trend === 'decrease' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                        }`}>
                          {disease.trend === 'increase' ? '↗' : disease.trend === 'decrease' ? '↘' : '→'} {Math.abs(parseFloat(disease.change))}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Municipal Comparison */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900">Municipal Hotspots</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {analysisData.municipalComparison.map((item: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 capitalize mb-2">{item.disease}</h4>
                <div className="text-sm text-gray-600">
                  <div>Highest burden: <span className="font-medium text-gray-900">{item.highestMunicipality}</span></div>
                  <div>Cases: <span className="font-medium text-gray-900">{item.highestCount}</span></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recommendations */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-green-900">Action Recommendations</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-green-800 mb-3">Immediate Actions</h4>
              <ul className="space-y-2">
                {analysisData.recommendations.immediate.map((action: string, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-green-700">{action}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-green-800 mb-3">Prevention Strategies</h4>
              <ul className="space-y-2">
                {analysisData.recommendations.prevention.map((strategy: string, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-green-700">{strategy}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Monitoring Thresholds */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h5v-5H4v5zM13 13h5l-5 5v-5zM4 13h5V8H4v5zM13 7h5l-5 5V7zM4 7h5V2H4v5z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900">Alert Thresholds</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {analysisData.recommendations.monitoring.map((item: any, index: number) => (
              <div key={index} className="border border-orange-200 rounded-lg p-4 bg-orange-50">
                <h4 className="font-semibold text-orange-900 capitalize">{item.disease}</h4>
                <div className="text-sm text-orange-700 mt-1">
                  <div>Alert at: <span className="font-bold">{item.threshold}</span> cases</div>
                  <div className="mt-1 text-xs">{item.action}</div>
                </div>
              </div>
            ))}
          </div>
        </div>



        {/* AI Conclusion - Always at the bottom */}
        <div className="bg-gradient-to-r from-slate-50 to-gray-50 border-2 border-slate-300 rounded-xl p-6 mt-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-slate-900">Analysis Conclusion</h3>
            <div className="ml-auto">
              <span className="bg-slate-100 text-slate-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                Final Summary
              </span>
            </div>
          </div>
          <div className="bg-white rounded-lg p-6 border-2 border-slate-200 shadow-sm">
            <div className="prose max-w-none text-sm leading-relaxed text-slate-700 whitespace-pre-wrap">
              {analysisData.aiConclusion || "Analysis conclusion will be generated based on the comprehensive disease surveillance data provided above."}
            </div>
          </div>
          <div className="mt-4 flex items-center justify-between text-xs text-slate-500">
            <div>Generated by HealthRadar AI Analysis System</div>
            <div>Report Date: {new Date().toLocaleDateString()} • {new Date().toLocaleTimeString()}</div>
          </div>
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-bold">AI Predictive Analysis</h2>
                <p className="text-blue-100 text-sm">Advanced disease surveillance insights • Global view across all municipalities</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-blue-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Modal Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {analysisResult.loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#143D60] mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Analyzing Disease Data...</h3>
                <p className="text-gray-500">AI is processing your disease surveillance data</p>
              </div>
            </div>
          )}

          {analysisResult.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-start gap-3">
                <svg className="w-6 h-6 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">Analysis Failed</h3>
                  <p className="text-red-700 mb-4">{analysisResult.error}</p>
                  <button
                    onClick={handleRetry}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Retry Analysis
                  </button>
                </div>
              </div>
            </div>
          )}

          {analysisResult.analysis && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-green-800 font-semibold">Analysis Complete</span>
                  <div className="ml-auto text-xs text-green-600">
                    Generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>

              {/* Professional Analysis Display */}
              <AnalysisDisplay analysisData={JSON.parse(analysisResult.analysis)} />

              <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <button
                  onClick={handleRetry}
                  className="px-4 py-2 text-[#143D60] border border-[#143D60] rounded-lg hover:bg-[#143D60] hover:text-white transition-colors"
                >
                  Regenerate Analysis
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-[#143D60] text-white rounded-lg hover:bg-[#1e4a6b] transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}

          {!analysisResult.loading && !analysisResult.error && !analysisResult.analysis && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Ready for Analysis</h3>
              <p className="text-gray-500 mb-4">Click the button below to generate AI insights</p>
              <button
                onClick={generateAIAnalysis}
                className="bg-[#143D60] text-white px-6 py-2 rounded-lg hover:bg-[#1e4a6b] transition-colors"
              >
                Generate Analysis
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
